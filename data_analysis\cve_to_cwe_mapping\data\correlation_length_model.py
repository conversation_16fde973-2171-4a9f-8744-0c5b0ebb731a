from transformers import AutoTokenizer
from sklearn.metrics import f1_score
from scipy.stats import pearsonr, spearmanr
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

#load dataset
df = pd.read_csv('data/data/csv')
description=df['description'].tolist()
y_true=np.load("y_true.npy")
y_pred=np.load("y_pred.npy")
_, tokenizer = initialize_selected_model(
   model_choice=model_choice,
   labels=label_list,
   use_adapter=use_adapter,
   hf_token=hf_token
   )


sample_lengths=[len(tokenizer.encode(text, truncation=True)) for text in df['description']]
sample_fls=[f1_score(y_true[i], y_pred[i], average='micro', zero_division=0)
    for i in range(len(y_true))
]
pearson_corr, p_pearson = pearsonr(sample_lengths, sample_fls)
spearman_corr, p_spearman = spearmanr(sample_lengths, sample_fls)
# 3. Correlation analysis
pearson_corr, p_pearson = pearsonr(sample_lengths, sample_fls)
spearman_corr, p_spearman = spearmanr(sample_lengths, sample_fls)

print(f" Pearson Correlation: {pearson_corr:.4f} (p={p_pearson:.4e})")
print(f" Spearman Correlation: {spearman_corr:.4f} (p={p_spearman:.4e})")

# 4. Create DataFrame for binning
df = pd.DataFrame({
    "length": sample_lengths,
    "f1": sample_fls
})

# Binning selon les quantiles
df['bin'] = pd.qcut(df['length'], q=4, labels=['short', 'medium-short', 'medium-long', 'long'])

# Moyenne F1 par bin
grouped = df.groupby('bin')['f1'].mean()

# 5. Visualization
plt.figure(figsize=(7, 5))
grouped.plot(kind='bar', edgecolor='black')
plt.title("F1-score moyen par longueur de description (bin)")
plt.xlabel("Bin de longueur de tokens")
plt.ylabel("F1-score moyen")
plt.grid(axis='y')
plt.tight_layout()
plt.show()


