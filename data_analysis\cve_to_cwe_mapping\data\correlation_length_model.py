from transformers import AutoTokenizer
from sklearn.metrics import f1_score
from scipy.stats import pearsonr, spearmanr
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

#load dataset
df = pd.read_csv('data/data/csv')
description=[...]
y_true=[...]
y_pred=[...]
tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
sample_lengths=[len(tokenizer.encode(text, truncation=True)) for text in df['description']]
sample_fls=[f1_score(y_true[i], y_pred[i], averag='binary') for i in range(len(y_true))]
pearson_corr, p_pearson = pearsonr(sample_lengths, sample_f1s)
spearman_corr, p_spearman = spearmanr(sample_lengths, sample_f1s)
# 3. Correlation analysis
pearson_corr, p_pearson = pearsonr(sample_lengths, sample_f1s)
spearman_corr, p_spearman = spearmanr(sample_lengths, sample_f1s)

print(f"📊 Pearson Correlation: {pearson_corr:.4f} (p={p_pearson:.4e})")
print(f"📊 Spearman Correlation: {spearman_corr:.4f} (p={p_spearman:.4e})")

# 4. Create DataFrame for binning
df = pd.DataFrame({
    "length": sample_lengths,
    "f1": sample_f1s
})

# Binning selon les quantiles
df['bin'] = pd.qcut(df['length'], q=4, labels=['short', 'medium-short', 'medium-long', 'long'])

# Moyenne F1 par bin
grouped = df.groupby('bin')['f1'].mean()

# 5. Visualization
plt.figure(figsize=(7, 5))
grouped.plot(kind='bar', edgecolor='black')
plt.title("F1-score moyen par longueur de description (bin)")
plt.xlabel("Bin de longueur de tokens")
plt.ylabel("F1-score moyen")
plt.grid(axis='y')
plt.tight_layout()
plt.show()
