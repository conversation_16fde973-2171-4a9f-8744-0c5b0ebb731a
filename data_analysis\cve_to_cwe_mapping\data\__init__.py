import os
import requests
from tqdm import tqdm
import zipfile
import argparse
script_dir = os.path.dirname(__file__)

CVE2CAPEC_BASE_URL = "https://raw.githubusercontent.com/Galeax/CVE2CAPEC/main/database/"
CVE2CAPEC_DIR = os.path.join(script_dir, "CVE2CAPEC")
CVE2CAPEC_YEARS = list(range(1999, 2026))

NVD_BASE_URL = "https://nvd.nist.gov/feeds/json/cve/2.0/"
NVD_ZIP_DIR = os.path.join(script_dir, "nvd_zip")
NVD_EXTRACT_DIR = os.path.join(script_dir, "nvd")
NVD_YEARS = list(range(2002, 2026))

CACHE_CVE_TO_CWE_DELAY = "https://www.dropbox.com/scl/fi/9jax3o67j1jht2iikxv2m/cache_cve_to_cwe_delay.zip?rlkey=1hb21svp57xbvrwj5pmniev1k&st=v3ff0zee&dl=1"
CACHE_CVE_TO_CWE_DELAY_DIR = os.path.join(script_dir, "..", "data_analysis", "cve_to_cwe_delay")

TECHNIQUES_ASSOCIATION_URL = "https://raw.githubusercontent.com/Galeax/CVE2CAPEC/main/resources/techniques_association.json"
TECHNIQUES_ASSOCIATION_DIR = os.path.join(script_dir, "techniques_association.json")

def download_techniques_association(dest):
    if not os.path.exists(dest):
        print(f"📥 Downloading techniques association file: {dest}")
        success = download_file(TECHNIQUES_ASSOCIATION_URL, dest)
        if success:
            print(f"✅ Techniques association file downloaded successfully: {dest}")
        else:
            print(f"❌ Failed to download techniques association file: {dest}")
    else:
        print(f"✅ Techniques association file already exists: {dest}")

def download_cache_cve_to_cwe_delay(dest):
    if not os.path.exists(dest):
        print(f"📥 Downloading cache file: {dest}")
        success = download_file(CACHE_CVE_TO_CWE_DELAY, dest)
        if success:
            print(f"✅ Cache file downloaded successfully: {dest}")
        else:
            print(f"❌ Failed to download cache file: {dest}")
    else:
        print(f"✅ Cache file already exists: {dest}")


def download_file(url, dest, num_max_trials=3):
    # ADD number of trials max
    for trial in range(num_max_trials):
        response = requests.get(url, stream=True)
        if response.status_code != 200:
            print(f"❌ Failed to download {url}, trial {trial + 1}/{num_max_trials}. Status code: {response.status_code}")
            continue

        total = int(response.headers.get('content-length', 0))
        with open(dest, 'wb') as file, tqdm(
            desc=f"⬇️  {os.path.basename(dest)}",
            total=total,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as bar:
            for data in response.iter_content(chunk_size=1024):
                size = file.write(data)
                bar.update(size)
        return True


def ensure_cve2capec_files(force=False):
    os.makedirs(CVE2CAPEC_DIR, exist_ok=True)
    for year in CVE2CAPEC_YEARS:
        fname = f"CVE-{year}.jsonl"
        fpath = os.path.join(CVE2CAPEC_DIR, fname)
        if not os.path.exists(fpath) or force:
            url = CVE2CAPEC_BASE_URL + fname
            print(f"📁 Downloading missing CVE2CAPEC file: {fname}")
            download_file(url, fpath)
        else:
            print(f"✅ Already exists: {fname}")


def ensure_nvd_jsons(force=False):
    os.makedirs(NVD_ZIP_DIR, exist_ok=True)
    os.makedirs(NVD_EXTRACT_DIR, exist_ok=True)

    for year in NVD_YEARS:
        zip_name = f"nvdcve-2.0-{year}.json.zip"
        json_name = f"nvdcve-2.0-{year}.json"
        zip_path = os.path.join(NVD_ZIP_DIR, zip_name)
        json_path = os.path.join(NVD_EXTRACT_DIR, json_name)

        if not os.path.exists(json_path) or force:
            if not os.path.exists(zip_path) or force:
                url = NVD_BASE_URL + zip_name
                print(f"📥 Downloading NVD data for {year}")
                download_file(url, zip_path)
            else:
                print(f"✅ Zip already exists: {zip_name}")

            # Unzip
            try:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(NVD_EXTRACT_DIR)
                print(f"📂 Extracted: {json_name}")
            except zipfile.BadZipFile:
                print(f"❌ Bad zip file: {zip_path}")
        else:
            print(f"✅ Already extracted: {json_name}")
        #  Remove zip files after extraction
        if os.path.exists(zip_path):
            os.remove(zip_path)
    if os.path.exists(NVD_ZIP_DIR):
        print(f"🗑️ Removed zip directory: {NVD_ZIP_DIR}")
        os.rmdir(NVD_ZIP_DIR)

def ensure_cache_cve_to_cwe_delay():
    os.makedirs(CACHE_CVE_TO_CWE_DELAY_DIR, exist_ok=True)
    cache_file = os.path.join(CACHE_CVE_TO_CWE_DELAY_DIR, "cache_cve_to_cwe_delay.zip")
    download_cache_cve_to_cwe_delay(cache_file)
    if os.path.exists(cache_file):
        with zipfile.ZipFile(cache_file, 'r') as zip_ref:
            zip_ref.extractall(CACHE_CVE_TO_CWE_DELAY_DIR)
        print(f"📂 Extracted cache to: {CACHE_CVE_TO_CWE_DELAY_DIR}")
        os.remove(cache_file)
        print(f"🗑️ Removed zip file: {cache_file}")

def ensure_techniques_association(force=False):
    os.makedirs(os.path.dirname(TECHNIQUES_ASSOCIATION_DIR), exist_ok=True)
    if not os.path.exists(TECHNIQUES_ASSOCIATION_DIR) or force:
        print(f"📥 Downloading techniques association file: {TECHNIQUES_ASSOCIATION_DIR}")
        download_file(TECHNIQUES_ASSOCIATION_URL, TECHNIQUES_ASSOCIATION_DIR)
    else:
        print(f"✅ Techniques association file already exists: {TECHNIQUES_ASSOCIATION_DIR}")


def main():
    parser = argparse.ArgumentParser(description="Initialize CVE2CAPEC and NVD data files.")
    parser.add_argument("--force", action="store_true",
                        help="Force re-download and extraction of files, even if they already exist.")
    args = parser.parse_args()
    print("📦 Initializing CVE2CAPEC + NVD data...")
    ensure_cve2capec_files(args.force)
    ensure_nvd_jsons(args.force)
    ensure_cache_cve_to_cwe_delay() # not updated typically, no need to have force option
    ensure_techniques_association(args.force)
    print("✅ All files ready.")


if __name__ == "__main__":
    main()
