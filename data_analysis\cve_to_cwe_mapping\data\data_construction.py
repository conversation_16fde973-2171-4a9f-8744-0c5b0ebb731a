import os
import requests
from tqdm import tqdm
import zipfile
import argparse
import re
import pandas as pd
import json
script_dir = os.path.dirname(__file__)

CVE2CAPEC_BASE_URL = "https://raw.githubusercontent.com/Galeax/CVE2CAPEC/main/database/"
CVE2CAPEC_DIR = os.path.join(script_dir, "CVE2CAPEC")
CVE2CAPEC_YEARS = list(range(1999, 2026))

NVD_BASE_URL = "https://nvd.nist.gov/feeds/json/cve/2.0/"
NVD_ZIP_DIR = os.path.join(script_dir, "nvd_zip")
NVD_EXTRACT_DIR = os.path.join(script_dir, "nvd")
NVD_YEARS = list(range(2002, 2026))

CACHE_CVE_TO_CWE_DELAY = "https://www.dropbox.com/scl/fi/9jax3o67j1jht2iikxv2m/cache_cve_to_cwe_delay.zip?rlkey=1hb21svp57xbvrwj5pmniev1k&st=v3ff0zee&dl=1"
CACHE_CVE_TO_CWE_DELAY_DIR = os.path.join(script_dir, "..", "data_analysis", "cve_to_cwe_delay")

TECHNIQUES_ASSOCIATION_URL = "https://raw.githubusercontent.com/Galeax/CVE2CAPEC/main/resources/techniques_association.json"
TECHNIQUES_ASSOCIATION_DIR = os.path.join(script_dir, "techniques_association.json")

def download_techniques_association(dest):
    if not os.path.exists(dest):
        print(f"📥 Downloading techniques association file: {dest}")
        success = download_file(TECHNIQUES_ASSOCIATION_URL, dest)
        if success:
            print(f"✅ Techniques association file downloaded successfully: {dest}")
        else:
            print(f"❌ Failed to download techniques association file: {dest}")
    else:
        print(f"✅ Techniques association file already exists: {dest}")

def download_cache_cve_to_cwe_delay(dest):
    if not os.path.exists(dest):
        print(f"📥 Downloading cache file: {dest}")
        success = download_file(CACHE_CVE_TO_CWE_DELAY, dest)
        if success:
            print(f"✅ Cache file downloaded successfully: {dest}")
        else:
            print(f"❌ Failed to download cache file: {dest}")
    else:
        print(f"✅ Cache file already exists: {dest}")


def download_file(url, dest, num_max_trials=3):
    """Télécharge un fichier avec gestion d'erreur améliorée."""
    for trial in range(num_max_trials):
        try:
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code != 200:
                print(f"❌ Failed to download {url}, trial {trial + 1}/{num_max_trials}. Status code: {response.status_code}")
                if trial == num_max_trials - 1:  # Dernier essai
                    print(f"⚠️ Skipping {os.path.basename(dest)} after {num_max_trials} failed attempts")
                    return False
                continue

            total = int(response.headers.get('content-length', 0))
            with open(dest, 'wb') as file, tqdm(
                desc=f"⬇️  {os.path.basename(dest)}",
                total=total,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as bar:
                for data in response.iter_content(chunk_size=1024):
                    size = file.write(data)
                    bar.update(size)

            # Vérifier que le fichier a été créé et n'est pas vide
            if os.path.exists(dest) and os.path.getsize(dest) > 0:
                return True
            else:
                print(f"❌ Downloaded file is empty or corrupted: {dest}")
                if os.path.exists(dest):
                    os.remove(dest)
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ Network error downloading {url}, trial {trial + 1}/{num_max_trials}: {e}")
            if trial == num_max_trials - 1:
                return False
        except Exception as e:
            print(f"❌ Unexpected error downloading {url}: {e}")
            return False

    return False


def ensure_cve2capec_files(force=False):
    os.makedirs(CVE2CAPEC_DIR, exist_ok=True)
    failed_downloads = []

    for year in CVE2CAPEC_YEARS:
        fname = f"CVE-{year}.jsonl"
        fpath = os.path.join(CVE2CAPEC_DIR, fname)
        if not os.path.exists(fpath) or force:
            url = CVE2CAPEC_BASE_URL + fname
            print(f"📁 Downloading missing CVE2CAPEC file: {fname}")
            success = download_file(url, fpath)
            if not success:
                failed_downloads.append(fname)
        else:
            print(f"✅ Already exists: {fname}")

    if failed_downloads:
        print(f"\n⚠️ Failed to download {len(failed_downloads)} CVE2CAPEC files:")
        for fname in failed_downloads:
            print(f"   - {fname}")
        print("💡 You can continue with the available files or retry later.")


def ensure_nvd_jsons(force=False):
    os.makedirs(NVD_ZIP_DIR, exist_ok=True)
    os.makedirs(NVD_EXTRACT_DIR, exist_ok=True)

    for year in NVD_YEARS:
        zip_name = f"nvdcve-2.0-{year}.json.zip"
        json_name = f"nvdcve-2.0-{year}.json"
        zip_path = os.path.join(NVD_ZIP_DIR, zip_name)
        json_path = os.path.join(NVD_EXTRACT_DIR, json_name)

        if not os.path.exists(json_path) or force:
            download_success = False

            if not os.path.exists(zip_path) or force:
                url = NVD_BASE_URL + zip_name
                print(f"📥 Downloading NVD data for {year}")
                download_success = download_file(url, zip_path)

                if not download_success:
                    print(f"⚠️ Skipping {year} due to download failure")
                    continue
            else:
                print(f"✅ Zip already exists: {zip_name}")
                download_success = True

            # Unzip seulement si le téléchargement a réussi et le fichier existe
            if download_success and os.path.exists(zip_path):
                try:
                    # Vérifier que le fichier zip est valide
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(NVD_EXTRACT_DIR)
                    print(f"📂 Extracted: {json_name}")
                except zipfile.BadZipFile:
                    print(f"❌ Bad zip file: {zip_path}")
                    if os.path.exists(zip_path):
                        os.remove(zip_path)
                        print(f"🗑️ Removed corrupted zip: {zip_path}")
                except Exception as e:
                    print(f"❌ Error extracting {zip_path}: {e}")
            else:
                print(f"⚠️ Skipping extraction for {year} (download failed or file missing)")
        else:
            print(f"✅ Already extracted: {json_name}")

        # Remove zip files after extraction (seulement si extraction réussie)
        if os.path.exists(zip_path) and os.path.exists(json_path):
            os.remove(zip_path)
    if os.path.exists(NVD_ZIP_DIR):
        print(f"🗑️ Removed zip directory: {NVD_ZIP_DIR}")
        os.rmdir(NVD_ZIP_DIR)

def ensure_cache_cve_to_cwe_delay():
    os.makedirs(CACHE_CVE_TO_CWE_DELAY_DIR, exist_ok=True)
    cache_file = os.path.join(CACHE_CVE_TO_CWE_DELAY_DIR, "cache_cve_to_cwe_delay.zip")
    download_cache_cve_to_cwe_delay(cache_file)
    if os.path.exists(cache_file):
        with zipfile.ZipFile(cache_file, 'r') as zip_ref:
            zip_ref.extractall(CACHE_CVE_TO_CWE_DELAY_DIR)
        print(f"📂 Extracted cache to: {CACHE_CVE_TO_CWE_DELAY_DIR}")
        os.remove(cache_file)
        print(f"🗑️ Removed zip file: {cache_file}")

def ensure_techniques_association(force=False):
    os.makedirs(os.path.dirname(TECHNIQUES_ASSOCIATION_DIR), exist_ok=True)
    if not os.path.exists(TECHNIQUES_ASSOCIATION_DIR) or force:
        print(f"📥 Downloading techniques association file: {TECHNIQUES_ASSOCIATION_DIR}")
        download_file(TECHNIQUES_ASSOCIATION_URL, TECHNIQUES_ASSOCIATION_DIR)
    else:
        print(f"✅ Techniques association file already exists: {TECHNIQUES_ASSOCIATION_DIR}")

def extract_parent_child_mapping(file_path, keyword="CWE"):
    parent_map={}
    stack=[]
    with open(file_path, encoding="utf-8") as f:
        for line in f:
            match=re.search(f"{keyword}-\d+", line)
            if not match:
                continue
            node_id = match.group(0)

            # indentation => niveau
            indent = len(line) - len(line.lstrip(" "))

            # mettre à jour la pile de parents
            while stack and stack[-1][1] >= indent:
                stack.pop()

            if stack:
                parent = stack[-1][0]
                parent_map[node_id] = parent

            stack.append((node_id, indent))
    return parent_map

def get_root_id(node_id, parent_map):
    """Trouve l'ID racine d'un nœud en remontant la hiérarchie."""
    while node_id in parent_map:
        node_id = parent_map[node_id]
    return node_id

def get_all_sublevels(node_id, parent_map):
    """Trouve TOUS les sous-niveaux (niveaux 1,2,3,4,5,6) d'un nœud, excluant la racine."""
    if not node_id:
        return []

    # Remonter jusqu'à la racine pour construire le chemin complet
    path = [node_id]
    current = node_id

    while current in parent_map:
        parent = parent_map[current]
        path.append(parent)
        current = parent

    # path = [node_original, parent1, parent2, ..., racine]
    # Retourner tous les éléments SAUF la racine (dernier élément)
    if len(path) > 1:
        return path[:-1]  # Tous sauf la racine
    else:
        return []  # Si c'est déjà la racine, pas de sous-niveaux

def load_cve_data():
    """Charge les données CVE depuis les fichiers NVD et CVE2CAPEC."""
    print("📊 Loading CVE data...")

    # Charger les données NVD (CVE avec CWE)
    nvd_data = []
    nvd_dir = os.path.join(script_dir, "nvd")

    if not os.path.exists(nvd_dir):
        print(f"❌ NVD directory not found: {nvd_dir}")
        return None

    # Limiter à quelques années pour test
    test_years = [2020, 2021, 2022, 2023, 2024]
    for year in test_years:
        nvd_file = os.path.join(nvd_dir, f"nvdcve-2.0-{year}.json")
        if os.path.exists(nvd_file):
            print(f"📄 Loading {nvd_file}")
            with open(nvd_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # Format NVD 2.0 (nouveau format)
                if 'vulnerabilities' in data:
                    for item in data['vulnerabilities']:
                        cve = item.get('cve', {})
                        cve_id = cve.get('id', '')

                        # Description
                        descriptions = cve.get('descriptions', [])
                        desc_text = ''
                        for desc in descriptions:
                            if desc.get('lang') == 'en':
                                desc_text = desc.get('value', '')
                                break

                        # CWE
                        cwe_ids = []
                        weaknesses = cve.get('weaknesses', [])
                        for weakness in weaknesses:
                            for desc in weakness.get('description', []):
                                value = desc.get('value', '')
                                if value.startswith('CWE-'):
                                    cwe_ids.append(value)

                        nvd_data.append({
                            'CVE_ID': cve_id,
                            'description': desc_text,
                            'CWE_ID': cwe_ids[0] if cwe_ids else None
                        })

                # Format NVD 1.1 (ancien format - pour compatibilité)
                elif 'CVE_Items' in data:
                    for cve in data.get('CVE_Items', []):
                        cve_id = cve.get('cve', {}).get('CVE_data_meta', {}).get('ID', '')
                        description = cve.get('cve', {}).get('description', {}).get('description_data', [])
                        desc_text = ' '.join([d.get('value', '') for d in description])

                        # Extraire CWE
                        cwe_ids = []
                        problemtype = cve.get('cve', {}).get('problemtype', {}).get('problemtype_data', [])
                        for pt in problemtype:
                            for desc in pt.get('description', []):
                                value = desc.get('value', '')
                                if value.startswith('CWE-'):
                                    cwe_ids.append(value)

                        nvd_data.append({
                            'CVE_ID': cve_id,
                            'description': desc_text,
                            'CWE_ID': cwe_ids[0] if cwe_ids else None
                        })

    # Charger les données CVE2CAPEC
    capec_data = {}
    cve2capec_dir = os.path.join(script_dir, "CVE2CAPEC")

    if os.path.exists(cve2capec_dir):
        for year in range(1999, 2026):
            capec_file = os.path.join(cve2capec_dir, f"CVE-{year}.jsonl")
            if os.path.exists(capec_file):
                print(f"📄 Loading {capec_file}")
                with open(capec_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            data = json.loads(line.strip())
                            # Format: {"CVE-2020-5179": {"CWE": ["707", "74"], "CAPEC": ["28", "7"]}}
                            for cve_id, details in data.items():
                                capec_ids = details.get('CAPEC', [])
                                if capec_ids:
                                    capec_data[cve_id] = f"CAPEC-{capec_ids[0]}"
                        except json.JSONDecodeError:
                            continue

    # Fusionner les données
    print("🔗 Merging CVE, CWE, and CAPEC data...")
    merged_data = []
    for nvd_item in nvd_data:
        cve_id = nvd_item['CVE_ID']
        capec_id = capec_data.get(cve_id)

        merged_data.append({
            'CVE_ID': cve_id,
            'description': nvd_item['description'],
            'CWE_ID': nvd_item['CWE_ID'],
            'CAPEC_ID': capec_id
        })

    return pd.DataFrame(merged_data)

def generate_hierarchy_files():
    """Génère les fichiers de hiérarchie CWE et CAPEC."""
    print("🌳 Generating hierarchy files...")

    # Générer cwe_tree.txt
    cwe_csv = os.path.join(script_dir, "..", "..", "..", "finetuner", "cwe_hierarchy.csv")
    cwe_tree_file = os.path.join(script_dir, "cwe_tree.txt")

    if os.path.exists(cwe_csv):
        os.system(f'python {os.path.join(script_dir, "..", "..", "..", "finetuner", "tree_hierarchy_cwe.py")} {cwe_csv} --export_terminal {cwe_tree_file} --depth 6')
        print(f"✅ Generated: {cwe_tree_file}")
    else:
        print(f"❌ CWE CSV not found: {cwe_csv}")

    # Générer capec_tree.txt
    capec_csv = os.path.join(script_dir, "..", "..", "..", "finetuner", "capec_hierarchy.csv")
    capec_tree_file = os.path.join(script_dir, "capec_tree.txt")

    if os.path.exists(capec_csv):
        os.system(f'python {os.path.join(script_dir, "..", "..", "..", "finetuner", "tree_hierarchy_capec.py")} {capec_csv} --export_terminal {capec_tree_file} --depth 6')
        print(f"✅ Generated: {capec_tree_file}")
    else:
        print(f"❌ CAPEC CSV not found: {capec_csv}")

def create_final_dataset():
    """Crée le dataset final avec les colonnes de niveaux hiérarchiques."""
    print("🎯 Creating final dataset with hierarchy levels...")

    # Vérifier que les données de base existent
    nvd_dir = os.path.join(script_dir, "nvd")
    cve2capec_dir = os.path.join(script_dir, "CVE2CAPEC")

    if not os.path.exists(nvd_dir) and not os.path.exists(cve2capec_dir):
        print("❌ No data directories found. Please run the script without --create_dataset first to download data:")
        print("   python data_analysis/cve_to_cwe_mapping/data/data_construction.py")
        return

    # Générer les fichiers de hiérarchie
    generate_hierarchy_files()

    # Charger les données CVE
    df = load_cve_data()
    if df is None:
        print("❌ Failed to load CVE data")
        print("💡 Make sure you have downloaded the data first:")
        print("   python data_analysis/cve_to_cwe_mapping/data/data_construction.py")
        return

    # Extraire les mappings parent-enfant
    cwe_tree_file = os.path.join(script_dir, "cwe_tree.txt")
    capec_tree_file = os.path.join(script_dir, "capec_tree.txt")

    cwe_parent_map = {}
    capec_parent_map = {}

    if os.path.exists(cwe_tree_file):
        cwe_parent_map = extract_parent_child_mapping(cwe_tree_file, keyword="CWE")
        print(f"✅ Loaded CWE hierarchy: {len(cwe_parent_map)} relationships")

    if os.path.exists(capec_tree_file):
        capec_parent_map = extract_parent_child_mapping(capec_tree_file, keyword="CAPEC")
        print(f"✅ Loaded CAPEC hierarchy: {len(capec_parent_map)} relationships")

    # Ajouter les colonnes de niveaux hiérarchiques
    print("📊 Adding hierarchy level columns...")

    #verification 1
    print("🔍 DEBUG INFO:")
    print(f"DataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"First few rows:\n{df.head()}")
    # Level 0 = Racine (niveau le plus haut)
    df["cwe_level_0"] = df["CWE"].apply(lambda x: get_root_id(x, cwe_parent_map) if pd.notnull(x) else x)
    df["capec_level_0"] = df["CAPEC"].apply(lambda x: get_root_id(x, capec_parent_map) if pd.notnull(x) else x)

    # Level 1 = TOUS les sous-niveaux (1,2,3,4,5,6) combinés, excluant la racine
    df["cwe_level_1"] = df["CWE"].apply(lambda x: get_all_sublevels(x, cwe_parent_map) if pd.notnull(x) else [])
    df["capec_level_1"] = df["CAPEC"].apply(lambda x: get_all_sublevels(x, capec_parent_map) if pd.notnull(x) else [])

    # Sauvegarder le dataset final
    output_file = os.path.join(script_dir, "cve_with_cwe_capec_levels.csv")
    df.to_csv(output_file, index=False)

    print(f"✅ Final dataset saved: {output_file}")
    print(f"📊 Dataset shape: {df.shape}")
    print(f"📊 Columns: {list(df.columns)}")

    # Statistiques
    print("\n📈 DATASET STATISTICS:")
    print(f"   - Total CVEs: {len(df)}")
    print(f"   - CVEs with CWE: {df['CWE'].notna().sum()}")
    print(f"   - CVEs with CAPEC: {df['CAPEC'].notna().sum()}")
    print(f"   - CVEs with both CWE and CAPEC: {(df['CWE'].notna() & df['CAPEC'].notna()).sum()}")

    print("\n🌳 HIERARCHY LEVEL STATISTICS:")
    print(f"   - Unique CWE Level 0 (roots): {df['cwe_level_0'].nunique()}")
    print(f"   - Unique CAPEC Level 0 (roots): {df['capec_level_0'].nunique()}")

    # Compter les éléments uniques dans les listes de level_1
    all_cwe_level_1 = set()
    for sublevels in df['cwe_level_1'].dropna():
        if isinstance(sublevels, list):
            all_cwe_level_1.update(sublevels)

    all_capec_level_1 = set()
    for sublevels in df['capec_level_1'].dropna():
        if isinstance(sublevels, list):
            all_capec_level_1.update(sublevels)

    print(f"   - Unique CWE Level 1 (all sub-levels): {len(all_cwe_level_1)}")
    print(f"   - Unique CAPEC Level 1 (all sub-levels): {len(all_capec_level_1)}")

    # Exemples de mapping
    print("\n🔍 EXAMPLES OF HIERARCHY MAPPING:")
    sample_cwe = df[df['CWE'].notna()].head(3)
    for _, row in sample_cwe.iterrows():
        if pd.notnull(row['CWE']):
            sublevels = row['cwe_level_1'] if isinstance(row['cwe_level_1'], list) else []
            sublevels_str = " → ".join(reversed(sublevels)) if sublevels else "None"
            print(f"   CWE: {row['CWE']} → All sub-levels: [{sublevels_str}] → Root: {row['cwe_level_0']}")

    sample_capec = df[df['CAPEC'].notna()].head(3)
    for _, row in sample_capec.iterrows():
        if pd.notnull(row['CAPEC']):
            sublevels = row['capec_level_1'] if isinstance(row['capec_level_1'], list) else []
            sublevels_str = " → ".join(reversed(sublevels)) if sublevels else "None"
            print(f"   CAPEC: {row['CAPEC_ID']} → All sub-levels: [{sublevels_str}] → Root: {row['capec_level_0']}")

def main():
    parser = argparse.ArgumentParser(description="Initialize CVE2CAPEC and NVD data files and create final dataset.")
    parser.add_argument("--force", action="store_true",
                        help="Force re-download and extraction of files, even if they already exist.")
    parser.add_argument("--create_dataset", action="store_true",
                        help="Create final dataset with CWE and CAPEC hierarchy levels.")
    args = parser.parse_args()

    if args.create_dataset:
        print("🎯 Creating final dataset with hierarchy levels...")
        create_final_dataset()
    else:
        print("📦 Initializing CVE2CAPEC + NVD data...")
        ensure_cve2capec_files(args.force)
        ensure_nvd_jsons(args.force)
        ensure_cache_cve_to_cwe_delay() # not updated typically, no need to have force option
        ensure_techniques_association(args.force)
        print("✅ All files ready.")
        print("\n💡 To create the final dataset with hierarchy levels, use:")
        print("   python data_construction.py --create_dataset")


if __name__ == "__main__":
    main()